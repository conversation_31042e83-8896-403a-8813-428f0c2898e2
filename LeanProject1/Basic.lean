import Mathlib

structure MyComplex where
  re : ℝ
  im : ℝ

def conj (z : MyComplex) : MyComplex :=
  ⟨z.re, -z.im⟩

def add (z w : MyComplex) : MyComplex :=
  ⟨z.re + w.re, z.im + w.im⟩

theorem sum_conj_eq_two_re (z : MyComplex) :
    add z (conj z) = ⟨2*z.re, 0⟩ := by
    -- z = ⟨a, b⟩
    -- conj z = ⟨a, -b⟩
    -- add z (conj z) = ⟨a + a, b + (-b)⟩ = ⟨2*a, 0⟩
    cases z with
    | mk re im => simp [add, conj, two_mul]

#check sum_conj_eq_two_re
